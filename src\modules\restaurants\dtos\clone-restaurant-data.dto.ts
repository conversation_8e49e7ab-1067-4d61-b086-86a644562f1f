import { IsNotEmpty, IsUUID } from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';

export class CloneRestaurantDataDto {
  @ApiProperty({
    description: 'ID of the source restaurant (data will be copied from this restaurant)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsNotEmpty()
  @IsUUID()
  sourceRestaurantId: string;

  @ApiProperty({
    description: 'ID of the target restaurant (data will be copied to this restaurant)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsNotEmpty()
  @IsUUID()
  targetRestaurantId: string;
}
