import { Roles } from '@auth/decorators/roles.decorator';
import { UserType } from '@auth/enums/user-type.enum';
import { Body, Controller, Post } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';

import { CloneRestaurantDataDto } from '../dtos/clone-restaurant-data.dto';
import { RestaurantsService } from '../restaurants.service';

@ApiTags('(Admin) Restaurants')
@Controller('admin/restaurants')
@Roles({ userType: UserType.AB_ADMIN, role: '*' })
export class AdminRestaurantsController {
  constructor(private readonly restaurantsService: RestaurantsService) {}

  @Post('clone-data')
  @ApiOperation({
    summary: 'Clone menu data from source restaurant to target restaurant',
    description:
      'Copy all menu, menu-item, menu-section, menu-item-option-group data and their mappings from source restaurant to target restaurant. Skip items with duplicate names.',
  })
  async cloneRestaurantData(@Body() cloneDto: CloneRestaurantDataDto) {
    return this.restaurantsService.cloneRestaurantData(cloneDto.sourceRestaurantId, cloneDto.targetRestaurantId);
  }
}
