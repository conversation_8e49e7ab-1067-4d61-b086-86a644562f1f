import { Pagination } from 'nestjs-typeorm-paginate';

import { UserMerchantId } from '@/common/decorators/user.decorator';
import { CheckNameExistsBrandDto, NameExistsResponseDto } from '@/common/dtos/check-name-exists.dto';
import { MerchantUserRole } from '@/modules/merchant-users/enums/merchant-users-role.enum';
import { Roles } from '@auth/decorators/roles.decorator';
import { UserType } from '@auth/enums/user-type.enum';
import { Body, Controller, Delete, Get, Param, ParseUUIDPipe, Post, Put, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { CloneRestaurantDataDto } from '../dtos/clone-restaurant-data.dto';
import { CreateRestaurantDto } from '../dtos/create-restaurant.dto';
import { ListReconciliationDto } from '../dtos/list-reconciliation.dto';
import { ListRestaurantDto } from '../dtos/list-restaurant.dto';
import { UpdateRestaurantDto } from '../dtos/update-restaurant.dto';
import { Restaurant } from '../entities/restaurant.entity';
import { RestaurantsService } from '../restaurants.service';

@ApiTags('Restaurants')
@Controller('restaurants')
export class RestaurantsController {
  constructor(private readonly restaurantsService: RestaurantsService) {}

  @Post()
  @Roles({ userType: UserType.MERCHANT_USER, role: [MerchantUserRole.SUPER_ADMIN] })
  create(
    @Body() createRestaurantDto: CreateRestaurantDto,
    @UserMerchantId() ownerId: string | null,
  ): Promise<Restaurant> {
    return this.restaurantsService.create(createRestaurantDto, ownerId);
  }

  @Post('check-name-exists')
  async checkExists(@Body() dto: CheckNameExistsBrandDto): Promise<NameExistsResponseDto> {
    const exists = await this.restaurantsService.checkNameExists(
      dto.brandId,
      dto.internalName,
      dto.publishedName,
      dto.excludeId,
    );
    return { exists };
  }

  @Get()
  @Roles({ userType: UserType.MERCHANT_USER, role: '*' })
  findAll(
    @Query() listRestaurantDto: ListRestaurantDto,
    @UserMerchantId() ownerId: string | null,
  ): Promise<Pagination<Restaurant>> {
    return this.restaurantsService.findAll(listRestaurantDto, ownerId);
  }

  @Get('reconciliations')
  @Roles({ userType: UserType.MERCHANT_USER, role: '*' })
  findAllReconciliations(@Query() listRestaurantDto: ListReconciliationDto, @UserMerchantId() ownerId: string | null) {
    return this.restaurantsService.findAllReconciliations(listRestaurantDto, ownerId);
  }

  @Get(':id')
  @Roles({ userType: UserType.MERCHANT_USER, role: '*' })
  findOne(@Param('id', ParseUUIDPipe) id: string, @UserMerchantId() ownerId: string | null): Promise<Restaurant> {
    return this.restaurantsService.findOne(id, ownerId);
  }

  @Put(':id')
  @Roles({ userType: UserType.MERCHANT_USER, role: [MerchantUserRole.MEMBER, MerchantUserRole.SUPER_ADMIN] })
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateRestaurantDto: UpdateRestaurantDto,
    @UserMerchantId() ownerId: string | null,
  ): Promise<Restaurant> {
    return this.restaurantsService.update(id, updateRestaurantDto, ownerId);
  }

  @Put('activate/:id')
  @Roles({ userType: UserType.MERCHANT_USER, role: [MerchantUserRole.SUPER_ADMIN] })
  activate(@Param('id', ParseUUIDPipe) id: string, @UserMerchantId() ownerId: string | null): Promise<Restaurant> {
    return this.restaurantsService.activate(id, ownerId);
  }

  @Put('deactivate/:id')
  @Roles({ userType: UserType.MERCHANT_USER, role: [MerchantUserRole.SUPER_ADMIN] })
  deactivate(@Param('id', ParseUUIDPipe) id: string, @UserMerchantId() ownerId: string | null): Promise<Restaurant> {
    return this.restaurantsService.deactivate(id, ownerId);
  }

  @Delete(':id')
  @Roles({ userType: UserType.MERCHANT_USER, role: [MerchantUserRole.SUPER_ADMIN] })
  delete(@Param('id', ParseUUIDPipe) id: string, @UserMerchantId() ownerId: string | null): Promise<Restaurant> {
    return this.restaurantsService.softDelete(id, ownerId);
  }

  @Post('clone-menu')
  @Roles({ userType: UserType.MERCHANT_USER, role: [MerchantUserRole.SUPER_ADMIN] })
  cloneMenu(@Body() cloneDto: CloneRestaurantDataDto) {
    return this.restaurantsService.cloneMenu(cloneDto.sourceRestaurantId, cloneDto.targetRestaurantId);
  }
}
